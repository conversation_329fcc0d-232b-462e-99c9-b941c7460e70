# SIMAK - Sistem Manajemen Tugas

SIMAK adalah sistem manajemen tugas berbasis web yang memungkinkan dosen dan mahasiswa untuk berinteraksi dalam proses pemberian dan pengumpulan tugas. Sistem ini dibangun menggunakan Laravel dengan fitur-fitur modern untuk mendukung pembelajaran online.

## 🚀 Fitur Utama

### Untuk Dosen
- **Dashboard Dosen**: Statistik lengkap tentang tugas dan submission
- **Manajemen Tugas**: Memb<PERSON>t, mengedit, dan menghapus tugas
- **Penilaian**: Memberikan nilai dan feedback untuk submission mahasiswa
- **Notifikasi**: Sistem notifikasi otomatis untuk submission baru
- **File Management**: Upload file pendukung untuk tugas

### Untuk Mahasiswa
- **Dashboard Mahasiswa**: Melihat tugas tersedia dan status submission
- **Pengumpulan Tugas**: Upload file tugas dengan komentar
- **Tracking**: Melihat status penilaian dan feedback dari dosen
- **Notifikasi**: Pemberitahuan tugas baru dan deadline mendekat
- **Download**: Mengunduh file tugas dan submission

### Fitur Umum
- **Authentication**: Login/Register dengan role-based access (Dosen/Mahasiswa)
- **Responsive Design**: Tampilan yang optimal di desktop dan mobile
- **Real-time Notifications**: Sistem notifikasi email dan database
- **File Upload**: Support multiple format file (PDF, DOC, DOCX, TXT, ZIP)
- **Deadline Management**: Tracking deadline dengan reminder otomatis

## 🛠️ Teknologi yang Digunakan

- **Framework**: Laravel 11
- **Authentication**: Laravel Breeze
- **Frontend**: Blade Templates + Tailwind CSS
- **Database**: SQLite (default) / MySQL
- **Notifications**: Laravel Notification System
- **File Storage**: Laravel Storage (Local/Public)

## 📋 Persyaratan Sistem

- PHP >= 8.2
- Composer
- Node.js & NPM
- SQLite atau MySQL

## 🔧 Instalasi

1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd simak
   ```

2. **Install Dependencies**
   ```bash
   composer install
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Database Setup**
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

5. **Build Assets**
   ```bash
   npm run build
   ```

6. **Storage Link**
   ```bash
   php artisan storage:link
   ```

7. **Run Application**
   ```bash
   php artisan serve
   ```

## 👥 Data Demo

Setelah menjalankan seeder, Anda dapat login dengan akun berikut:

### Akun Dosen
- **Email**: <EMAIL>
- **Password**: password
- **Role**: Dosen

- **Email**: <EMAIL>
- **Password**: password
- **Role**: Dosen

### Akun Mahasiswa
- **Email**: <EMAIL>
- **Password**: password
- **Role**: Mahasiswa

- **Email**: <EMAIL>
- **Password**: password
- **Role**: Mahasiswa

## 📁 Struktur Database

### Tabel Utama
- **users**: Data pengguna (dosen dan mahasiswa)
- **tasks**: Data tugas yang dibuat dosen
- **submissions**: Data pengumpulan tugas mahasiswa
- **notifications**: Sistem notifikasi

### Relasi
- User (Dosen) → hasMany → Tasks
- User (Mahasiswa) → hasMany → Submissions
- Task → hasMany → Submissions
- Task → belongsTo → User (Dosen)
- Submission → belongsTo → Task
- Submission → belongsTo → User (Mahasiswa)

## 🔔 Sistem Notifikasi

### Jenis Notifikasi
1. **Tugas Baru**: Dikirim ke semua mahasiswa ketika dosen membuat tugas baru
2. **Deadline Reminder**: Pengingat deadline tugas (dapat dikustomisasi)
3. **Submission Graded**: Notifikasi ketika tugas sudah dinilai

### Channel Notifikasi
- **Database**: Notifikasi tersimpan di database
- **Email**: Notifikasi dikirim via email (perlu konfigurasi SMTP)

## 🎨 Tampilan

Sistem menggunakan Tailwind CSS untuk styling dengan desain yang:
- **Clean & Modern**: Interface yang bersih dan mudah digunakan
- **Responsive**: Optimal di semua ukuran layar
- **Accessible**: Mengikuti standar aksesibilitas web
- **Consistent**: Konsistensi design pattern di seluruh aplikasi

## 🔒 Keamanan

- **Authentication**: Laravel Breeze dengan session-based auth
- **Authorization**: Role-based access control
- **File Upload**: Validasi tipe dan ukuran file
- **CSRF Protection**: Perlindungan dari CSRF attacks
- **Input Validation**: Validasi semua input pengguna

## 📝 Penggunaan

### Untuk Dosen
1. Login dengan akun dosen
2. Buat tugas baru dari dashboard atau menu Tugas
3. Monitor submission mahasiswa
4. Berikan nilai dan feedback

### Untuk Mahasiswa
1. Login dengan akun mahasiswa
2. Lihat daftar tugas yang tersedia
3. Upload file tugas sebelum deadline
4. Cek status penilaian dan feedback

## 🚀 Pengembangan Lanjutan

Fitur yang dapat dikembangkan:
- **Real-time Chat**: Komunikasi langsung dosen-mahasiswa
- **Video Conference**: Integrasi dengan platform video call
- **Plagiarism Checker**: Deteksi plagiarisme otomatis
- **Analytics**: Dashboard analytics yang lebih detail
- **Mobile App**: Aplikasi mobile native
- **API**: RESTful API untuk integrasi dengan sistem lain

## 📞 Support

Jika Anda mengalami masalah atau memiliki pertanyaan, silakan:
1. Buka issue di repository ini
2. Hubungi tim pengembang
3. Baca dokumentasi Laravel untuk referensi teknis

## 📄 Lisensi

Project ini menggunakan lisensi MIT. Silakan lihat file LICENSE untuk detail lengkap.

## 🎯 Struktur Project

```
simak/
├── app/
│   ├── Http/Controllers/
│   │   ├── DashboardController.php
│   │   ├── TaskController.php
│   │   └── SubmissionController.php
│   ├── Models/
│   │   ├── User.php
│   │   ├── Task.php
│   │   └── Submission.php
│   └── Notifications/
│       ├── NewTaskNotification.php
│       ├── TaskDeadlineNotification.php
│       └── SubmissionGradedNotification.php
├── database/
│   ├── migrations/
│   └── seeders/
├── resources/
│   └── views/
│       ├── dashboard/
│       ├── tasks/
│       └── submissions/
└── routes/
    └── web.php
```

## 🔧 Konfigurasi Email (Opsional)

Untuk mengaktifkan notifikasi email, tambahkan konfigurasi SMTP di file `.env`:

```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="SIMAK System"
```

## 📊 Fitur Dashboard

### Dashboard Dosen
- Total tugas yang dibuat
- Jumlah tugas aktif
- Total submission yang masuk
- Submission yang belum dinilai
- Daftar tugas terbaru
- Submission yang perlu dinilai

### Dashboard Mahasiswa
- Total submission yang dikumpulkan
- Submission yang sudah dinilai
- Rata-rata nilai
- Tugas dengan deadline mendekat
- Daftar tugas tersedia
- Riwayat submission

## 🎓 Panduan Penggunaan Lengkap

### Membuat Tugas (Dosen)
1. Masuk ke menu "Tugas" → "Buat Tugas Baru"
2. Isi form dengan lengkap:
   - Judul tugas yang jelas
   - Deskripsi detail dengan kriteria penilaian
   - Deadline yang realistis
   - File pendukung (opsional)
3. Klik "Buat Tugas"
4. Sistem akan mengirim notifikasi ke semua mahasiswa

### Mengumpulkan Tugas (Mahasiswa)
1. Lihat daftar tugas di dashboard atau menu "Tugas"
2. Klik "Lihat Detail" pada tugas yang ingin dikumpulkan
3. Klik "Kumpulkan Tugas"
4. Upload file dan tambahkan komentar
5. Centang pernyataan dan klik "Kumpulkan Tugas"

### Memberikan Nilai (Dosen)
1. Masuk ke menu "Submissions" atau lihat di dashboard
2. Klik "Nilai" pada submission yang ingin dinilai
3. Download dan review file submission
4. Berikan nilai (0-100) dan feedback
5. Klik "Simpan Nilai"
6. Sistem akan mengirim notifikasi ke mahasiswa

Selamat menggunakan SIMAK! 🎉
