<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Task;
use App\Models\Submission;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Notifications\SubmissionGradedNotification;

class SubmissionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = Auth::user();

        if ($user->isMahasiswa()) {
            $submissions = $user->submissions()->with('task')->latest()->paginate(10);
        } else {
            // Dosen melihat semua submission untuk tugas mereka
            $submissions = Submission::with(['task', 'mahasiswa'])
                ->whereHas('task', function($query) use ($user) {
                    $query->where('dosen_id', $user->id);
                })
                ->latest()
                ->paginate(10);
        }

        return view('submissions.index', compact('submissions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        // <PERSON><PERSON> mahasiswa yang bisa submit tugas
        if (!Auth::user()->isMahasiswa()) {
            abort(403, 'Unauthorized action.');
        }

        $taskId = $request->get('task_id');
        $task = Task::findOrFail($taskId);

        // Cek apakah sudah pernah submit
        $existingSubmission = Submission::where('task_id', $taskId)
            ->where('mahasiswa_id', Auth::id())
            ->first();

        if ($existingSubmission) {
            return redirect()->route('tasks.show', $task)
                ->with('error', 'Anda sudah mengumpulkan tugas ini!');
        }

        // Cek apakah deadline sudah lewat
        if ($task->isOverdue()) {
            return redirect()->route('tasks.show', $task)
                ->with('error', 'Deadline tugas sudah lewat!');
        }

        return view('submissions.create', compact('task'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Hanya mahasiswa yang bisa submit tugas
        if (!Auth::user()->isMahasiswa()) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'task_id' => 'required|exists:tasks,id',
            'file' => 'required|file|mimes:pdf,doc,docx,txt,zip|max:5120', // 5MB
            'comment' => 'nullable|string|max:1000',
        ]);

        $task = Task::findOrFail($request->task_id);

        // Cek apakah sudah pernah submit
        $existingSubmission = Submission::where('task_id', $request->task_id)
            ->where('mahasiswa_id', Auth::id())
            ->first();

        if ($existingSubmission) {
            return redirect()->route('tasks.show', $task)
                ->with('error', 'Anda sudah mengumpulkan tugas ini!');
        }

        // Cek deadline
        if ($task->isOverdue()) {
            return redirect()->route('tasks.show', $task)
                ->with('error', 'Deadline tugas sudah lewat!');
        }

        // Upload file
        $filePath = $request->file('file')->store('submissions', 'public');

        Submission::create([
            'task_id' => $request->task_id,
            'mahasiswa_id' => Auth::id(),
            'file_path' => $filePath,
            'comment' => $request->comment,
            'status' => 'submitted',
        ]);

        return redirect()->route('tasks.show', $task)
            ->with('success', 'Tugas berhasil dikumpulkan!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Submission $submission)
    {
        $user = Auth::user();

        // Mahasiswa hanya bisa lihat submission sendiri
        if ($user->isMahasiswa() && $submission->mahasiswa_id !== $user->id) {
            abort(403, 'Unauthorized action.');
        }

        // Dosen hanya bisa lihat submission untuk tugas mereka
        if ($user->isDosen() && $submission->task->dosen_id !== $user->id) {
            abort(403, 'Unauthorized action.');
        }

        $submission->load(['task', 'mahasiswa']);

        return view('submissions.show', compact('submission'));
    }

    /**
     * Show the form for editing the specified resource (grading).
     */
    public function edit(Submission $submission)
    {
        // Hanya dosen pembuat tugas yang bisa grade
        if (!Auth::user()->isDosen() || $submission->task->dosen_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $submission->load(['task', 'mahasiswa']);

        return view('submissions.grade', compact('submission'));
    }

    /**
     * Update the specified resource in storage (grading).
     */
    public function update(Request $request, Submission $submission)
    {
        // Hanya dosen pembuat tugas yang bisa grade
        if (!Auth::user()->isDosen() || $submission->task->dosen_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'score' => 'required|integer|min:0|max:100',
            'feedback' => 'nullable|string|max:1000',
        ]);

        $submission->update([
            'score' => $request->score,
            'feedback' => $request->feedback,
            'status' => 'graded',
        ]);

        // Send notification to mahasiswa
        $submission->mahasiswa->notify(new SubmissionGradedNotification($submission));

        return redirect()->route('submissions.show', $submission)
            ->with('success', 'Nilai berhasil diberikan dan notifikasi telah dikirim!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Submission $submission)
    {
        $user = Auth::user();

        // Mahasiswa hanya bisa hapus submission sendiri yang belum dinilai
        if ($user->isMahasiswa()) {
            if ($submission->mahasiswa_id !== $user->id || $submission->isGraded()) {
                abort(403, 'Unauthorized action.');
            }
        }
        // Dosen hanya bisa hapus submission untuk tugas mereka
        elseif ($user->isDosen()) {
            if ($submission->task->dosen_id !== $user->id) {
                abort(403, 'Unauthorized action.');
            }
        }

        // Hapus file
        if ($submission->file_path) {
            Storage::disk('public')->delete($submission->file_path);
        }

        $submission->delete();

        return redirect()->route('submissions.index')
            ->with('success', 'Submission berhasil dihapus!');
    }

    /**
     * Download submission file
     */
    public function download(Submission $submission)
    {
        $user = Auth::user();

        // Mahasiswa hanya bisa download submission sendiri
        if ($user->isMahasiswa() && $submission->mahasiswa_id !== $user->id) {
            abort(403, 'Unauthorized action.');
        }

        // Dosen hanya bisa download submission untuk tugas mereka
        if ($user->isDosen() && $submission->task->dosen_id !== $user->id) {
            abort(403, 'Unauthorized action.');
        }

        if (!$submission->file_path || !Storage::disk('public')->exists($submission->file_path)) {
            abort(404, 'File not found.');
        }

        return Storage::disk('public')->download($submission->file_path);
    }
}
