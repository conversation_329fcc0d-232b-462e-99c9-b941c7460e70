<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Task;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Notification;
use App\Notifications\NewTaskNotification;

class TaskController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = Auth::user();

        if ($user->isDosen()) {
            $tasks = $user->tasks()->latest()->paginate(10);
        } else {
            $tasks = Task::where('status', 'active')->latest()->paginate(10);
        }

        return view('tasks.index', compact('tasks'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Hanya dosen yang bisa membuat tugas
        if (!Auth::user()->isDosen()) {
            abort(403, 'Unauthorized action.');
        }

        return view('tasks.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // <PERSON>ya dosen yang bisa membuat tugas
        if (!Auth::user()->isDosen()) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'deadline' => 'required|date|after:now',
            'file' => 'nullable|file|mimes:pdf,doc,docx,txt|max:2048',
        ]);

        $filePath = null;
        if ($request->hasFile('file')) {
            $filePath = $request->file('file')->store('task-files', 'public');
        }

        $task = Task::create([
            'title' => $request->title,
            'description' => $request->description,
            'dosen_id' => Auth::id(),
            'deadline' => $request->deadline,
            'file_path' => $filePath,
            'status' => 'active',
        ]);

        // Send notification to all mahasiswa
        $mahasiswa = User::where('role', 'mahasiswa')->get();
        Notification::send($mahasiswa, new NewTaskNotification($task));

        return redirect()->route('tasks.index')->with('success', 'Tugas berhasil dibuat dan notifikasi telah dikirim!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Task $task)
    {
        $user = Auth::user();

        // Load submissions jika user adalah dosen pembuat tugas
        if ($user->isDosen() && $task->dosen_id === $user->id) {
            $task->load(['submissions.mahasiswa']);
        }

        // Cek apakah mahasiswa sudah submit tugas ini
        $userSubmission = null;
        if ($user->isMahasiswa()) {
            $userSubmission = $task->submissions()->where('mahasiswa_id', $user->id)->first();
        }

        return view('tasks.show', compact('task', 'userSubmission'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Task $task)
    {
        // Hanya dosen pembuat tugas yang bisa edit
        if (!Auth::user()->isDosen() || $task->dosen_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        return view('tasks.edit', compact('task'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Task $task)
    {
        // Hanya dosen pembuat tugas yang bisa update
        if (!Auth::user()->isDosen() || $task->dosen_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'deadline' => 'required|date',
            'status' => 'required|in:active,inactive',
            'file' => 'nullable|file|mimes:pdf,doc,docx,txt|max:2048',
        ]);

        $filePath = $task->file_path;
        if ($request->hasFile('file')) {
            // Hapus file lama jika ada
            if ($filePath) {
                Storage::disk('public')->delete($filePath);
            }
            $filePath = $request->file('file')->store('task-files', 'public');
        }

        $task->update([
            'title' => $request->title,
            'description' => $request->description,
            'deadline' => $request->deadline,
            'status' => $request->status,
            'file_path' => $filePath,
        ]);

        return redirect()->route('tasks.index')->with('success', 'Tugas berhasil diupdate!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Task $task)
    {
        // Hanya dosen pembuat tugas yang bisa hapus
        if (!Auth::user()->isDosen() || $task->dosen_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        // Hapus file jika ada
        if ($task->file_path) {
            Storage::disk('public')->delete($task->file_path);
        }

        $task->delete();

        return redirect()->route('tasks.index')->with('success', 'Tugas berhasil dihapus!');
    }
}
