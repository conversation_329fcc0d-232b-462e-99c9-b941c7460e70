<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Task;

class NewTaskNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $task;

    /**
     * Create a new notification instance.
     */
    public function __construct(Task $task)
    {
        $this->task = $task;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
                    ->subject('Tugas Baru: ' . $this->task->title)
                    ->greeting('Halo ' . $notifiable->name . '!')
                    ->line('Ada tugas baru yang telah dibuat oleh ' . $this->task->dosen->name)
                    ->line('**Judul <PERSON>:** ' . $this->task->title)
                    ->line('**Deadline:** ' . $this->task->deadline->format('d M Y H:i'))
                    ->line('**Deskripsi:** ' . \Str::limit($this->task->description, 200))
                    ->action('Lihat Tugas', route('tasks.show', $this->task))
                    ->line('Jangan lupa untuk mengumpulkan tugas sebelum deadline!')
                    ->salutation('Salam, Tim Simak');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'task_id' => $this->task->id,
            'task_title' => $this->task->title,
            'dosen_name' => $this->task->dosen->name,
            'deadline' => $this->task->deadline->toISOString(),
            'message' => 'Tugas baru "' . $this->task->title . '" telah dibuat oleh ' . $this->task->dosen->name,
            'action_url' => route('tasks.show', $this->task),
        ];
    }
}
