<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Submission;

class SubmissionGradedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $submission;

    /**
     * Create a new notification instance.
     */
    public function __construct(Submission $submission)
    {
        $this->submission = $submission;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $gradeText = $this->getGradeText($this->submission->score);

        return (new MailMessage)
                    ->subject('📊 Tugas Anda Telah Dinilai: ' . $this->submission->task->title)
                    ->greeting('Halo ' . $notifiable->name . '!')
                    ->line('Tugas Anda "' . $this->submission->task->title . '" telah dinilai oleh ' . $this->submission->task->dosen->name)
                    ->line('**Nilai:** ' . $this->submission->score . '/100 (' . $gradeText . ')')
                    ->when($this->submission->feedback, function ($mail) {
                        return $mail->line('**Feedback dari Dosen:**')
                                   ->line('"' . $this->submission->feedback . '"');
                    })
                    ->action('Lihat Detail Nilai', route('submissions.show', $this->submission))
                    ->line('Terima kasih atas kerja keras Anda!')
                    ->salutation('Salam, Tim Simak');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'submission_id' => $this->submission->id,
            'task_id' => $this->submission->task->id,
            'task_title' => $this->submission->task->title,
            'dosen_name' => $this->submission->task->dosen->name,
            'score' => $this->submission->score,
            'feedback' => $this->submission->feedback,
            'message' => 'Tugas "' . $this->submission->task->title . '" telah dinilai. Nilai: ' . $this->submission->score . '/100',
            'action_url' => route('submissions.show', $this->submission),
            'type' => 'submission_graded',
        ];
    }

    /**
     * Get grade text based on score
     */
    private function getGradeText(int $score): string
    {
        if ($score >= 90) return 'A - Sangat Baik';
        if ($score >= 80) return 'B - Baik';
        if ($score >= 70) return 'C - Cukup';
        if ($score >= 60) return 'D - Kurang';
        return 'E - Tidak Memenuhi';
    }
}
