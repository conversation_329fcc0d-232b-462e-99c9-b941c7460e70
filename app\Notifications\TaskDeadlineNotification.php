<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Task;

class TaskDeadlineNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $task;
    protected $hoursLeft;

    /**
     * Create a new notification instance.
     */
    public function __construct(Task $task, int $hoursLeft)
    {
        $this->task = $task;
        $this->hoursLeft = $hoursLeft;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $timeLeft = $this->hoursLeft <= 24
            ? $this->hoursLeft . ' jam'
            : round($this->hoursLeft / 24) . ' hari';

        return (new MailMessage)
                    ->subject('⚠️ Deadline Mendekat: ' . $this->task->title)
                    ->greeting('Halo ' . $notifiable->name . '!')
                    ->line('Deadline tugas "' . $this->task->title . '" akan berakhir dalam ' . $timeLeft . '!')
                    ->line('**Deadline:** ' . $this->task->deadline->format('d M Y H:i'))
                    ->line('**Dosen:** ' . $this->task->dosen->name)
                    ->action('Kumpulkan Tugas Sekarang', route('tasks.show', $this->task))
                    ->line('Jangan sampai terlambat! Segera kumpulkan tugas Anda.')
                    ->salutation('Salam, Tim Simak');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $timeLeft = $this->hoursLeft <= 24
            ? $this->hoursLeft . ' jam'
            : round($this->hoursLeft / 24) . ' hari';

        return [
            'task_id' => $this->task->id,
            'task_title' => $this->task->title,
            'dosen_name' => $this->task->dosen->name,
            'deadline' => $this->task->deadline->toISOString(),
            'hours_left' => $this->hoursLeft,
            'message' => 'Deadline tugas "' . $this->task->title . '" tinggal ' . $timeLeft . ' lagi!',
            'action_url' => route('tasks.show', $this->task),
            'type' => 'deadline_reminder',
        ];
    }
}
