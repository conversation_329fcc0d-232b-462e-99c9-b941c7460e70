<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Task;
use App\Models\User;

class TaskSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $dosen1 = User::where('role', 'dosen')->first();
        $dosen2 = User::where('role', 'dosen')->skip(1)->first();

        // Tasks from first dosen
        Task::create([
            'title' => 'Tugas Pemrograman Web - HTML & CSS',
            'description' => 'Buatlah sebuah website sederhana menggunakan HTML dan CSS dengan tema bebas. Website harus memiliki:

1. Minimal 3 halaman (Home, About, Contact)
2. Navigation menu yang responsive
3. Penggunaan CSS Grid atau Flexbox
4. Form contact yang fungsional
5. Desain yang menarik dan user-friendly

Kriteria Penilaian:
- Struktur HTML yang semantik (25%)
- Styling CSS yang rapi dan konsisten (25%)
- Responsive design (25%)
- Kreativitas dan estetika (25%)

Kumpulkan dalam bentuk file ZIP yang berisi semua file HTML, CSS, dan aset lainnya.',
            'dosen_id' => $dosen1->id,
            'deadline' => now()->addDays(7),
            'status' => 'active',
        ]);

        Task::create([
            'title' => 'Analisis Algoritma Sorting',
            'description' => 'Lakukan analisis perbandingan algoritma sorting berikut:

1. Bubble Sort
2. Selection Sort
3. Insertion Sort
4. Merge Sort
5. Quick Sort

Untuk setiap algoritma, analisis:
- Time Complexity (Best, Average, Worst Case)
- Space Complexity
- Implementasi dalam bahasa pemrograman pilihan
- Pengujian dengan dataset berbeda ukuran

Buatlah laporan dalam format PDF yang mencakup:
- Penjelasan teoritis setiap algoritma
- Implementasi kode
- Hasil pengujian dan grafik perbandingan
- Kesimpulan dan rekomendasi penggunaan

Laporan minimal 10 halaman dengan referensi yang valid.',
            'dosen_id' => $dosen1->id,
            'deadline' => now()->addDays(14),
            'status' => 'active',
        ]);

        // Tasks from second dosen
        Task::create([
            'title' => 'Desain Database E-Commerce',
            'description' => 'Rancanglah database untuk sistem e-commerce dengan fitur:

1. Manajemen produk (kategori, stok, harga)
2. Sistem user (customer, admin)
3. Keranjang belanja dan wishlist
4. Sistem pembayaran dan pengiriman
5. Review dan rating produk

Deliverables:
- ERD (Entity Relationship Diagram)
- Normalisasi database hingga 3NF
- DDL (Data Definition Language) script
- Sample data dan DML script
- Dokumentasi desain database

Gunakan tools seperti MySQL Workbench, draw.io, atau sejenisnya untuk membuat ERD.',
            'dosen_id' => $dosen2->id,
            'deadline' => now()->addDays(10),
            'status' => 'active',
        ]);

        Task::create([
            'title' => 'Implementasi REST API dengan Laravel',
            'description' => 'Buatlah REST API untuk sistem manajemen buku perpustakaan dengan fitur:

Endpoints yang harus dibuat:
1. Authentication (login, register, logout)
2. CRUD Books (Create, Read, Update, Delete)
3. CRUD Categories
4. Book borrowing system
5. Search dan filter books

Requirements:
- Menggunakan Laravel framework
- Implementasi JWT authentication
- Validation untuk semua input
- Response format JSON yang konsisten
- Error handling yang proper
- API documentation menggunakan Postman atau Swagger

Kriteria Penilaian:
- Struktur kode yang clean dan terorganisir (30%)
- Implementasi authentication dan authorization (25%)
- Functionality dan testing (25%)
- Documentation (20%)

Kumpulkan dalam bentuk repository GitHub dengan README yang lengkap.',
            'dosen_id' => $dosen2->id,
            'deadline' => now()->addDays(21),
            'status' => 'active',
        ]);

        // One past deadline task for testing
        Task::create([
            'title' => 'Tugas Terlambat - Konsep OOP',
            'description' => 'Tugas ini sudah melewati deadline untuk testing purposes.',
            'dosen_id' => $dosen1->id,
            'deadline' => now()->subDays(2),
            'status' => 'active',
        ]);
    }
}
