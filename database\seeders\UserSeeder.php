<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample dosen
        User::create([
            'name' => 'Dr. Ahmad Wijaya',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'dosen',
            'nip' => '198501012010011001',
        ]);

        User::create([
            'name' => 'Prof. Siti Nurhaliza',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'dosen',
            'nip' => '197803152005012002',
        ]);

        // Create sample mahasiswa
        User::create([
            'name' => '<PERSON><PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'mahasiswa',
            'nim' => '2021001001',
        ]);

        User::create([
            'name' => 'Andi Pratama',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'mahasiswa',
            'nim' => '2021001002',
        ]);

        User::create([
            'name' => 'Sari Dewi',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'mahasiswa',
            'nim' => '2021001003',
        ]);

        User::create([
            'name' => 'Riko Firmansyah',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'mahasiswa',
            'nim' => '2021001004',
        ]);
    }
}
