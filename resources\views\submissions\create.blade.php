<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Kumpul<PERSON> Tugas') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <!-- Info Tugas -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ $task->title }}</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mb-4">
                        <div>
                            <span class="font-medium">Dosen:</span> {{ $task->dosen->name }}
                        </div>
                        <div>
                            <span class="font-medium">Deadline:</span> 
                            <span class="{{ $task->isOverdue() ? 'text-red-600 font-semibold' : '' }}">
                                {{ $task->deadline->format('d M Y H:i') }}
                            </span>
                        </div>
                    </div>
                    
                    @if($task->deadline->diffInHours(now()) <= 24 && !$task->isOverdue())
                        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-yellow-800">Deadline Mendekat!</h3>
                                    <div class="mt-2 text-sm text-yellow-700">
                                        <p>Deadline tugas ini tinggal {{ $task->deadline->diffForHumans() }}. Segera kumpulkan tugas Anda!</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="prose max-w-none">
                        <h4 class="text-md font-semibold text-gray-900 mb-2">Deskripsi Tugas:</h4>
                        <div class="text-gray-700 whitespace-pre-line">{{ $task->description }}</div>
                    </div>

                    @if($task->file_path)
                        <div class="mt-4 pt-4 border-t">
                            <h4 class="text-md font-semibold text-gray-900 mb-2">File Lampiran dari Dosen:</h4>
                            <a href="{{ Storage::url($task->file_path) }}" target="_blank" class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-700 text-white font-bold rounded">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clip-rule="evenodd"></path>
                                </svg>
                                Download File
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Form Submit Tugas -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Upload Tugas Anda</h3>
                    
                    <form action="{{ route('submissions.store') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                        @csrf
                        <input type="hidden" name="task_id" value="{{ $task->id }}">

                        <!-- File Upload -->
                        <div>
                            <x-input-label for="file" :value="__('File Tugas')" />
                            <input id="file" name="file" type="file" class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100" accept=".pdf,.doc,.docx,.txt,.zip" required />
                            <x-input-error class="mt-2" :messages="$errors->get('file')" />
                            <p class="mt-1 text-sm text-gray-600">Upload file tugas Anda (PDF, DOC, DOCX, TXT, ZIP - Max: 5MB).</p>
                        </div>

                        <!-- Comment -->
                        <div>
                            <x-input-label for="comment" :value="__('Komentar (Opsional)')" />
                            <textarea id="comment" name="comment" rows="4" class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm" placeholder="Tambahkan komentar atau catatan untuk dosen...">{{ old('comment') }}</textarea>
                            <x-input-error class="mt-2" :messages="$errors->get('comment')" />
                            <p class="mt-1 text-sm text-gray-600">Anda dapat menambahkan komentar atau penjelasan tambahan tentang tugas yang dikumpulkan.</p>
                        </div>

                        <!-- Submission Guidelines -->
                        <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-blue-800">Panduan Pengumpulan Tugas</h3>
                                    <div class="mt-2 text-sm text-blue-700">
                                        <ul class="list-disc list-inside space-y-1">
                                            <li>Pastikan file yang diupload sesuai dengan format yang diminta</li>
                                            <li>Periksa kembali isi tugas sebelum mengumpulkan</li>
                                            <li>Setelah dikumpulkan, tugas tidak dapat diubah</li>
                                            <li>Anda akan mendapat notifikasi setelah tugas dinilai</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Confirmation -->
                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                            <div class="flex items-start">
                                <div class="flex items-center h-5">
                                    <input id="confirmation" name="confirmation" type="checkbox" class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" required>
                                </div>
                                <div class="ml-3 text-sm">
                                    <label for="confirmation" class="font-medium text-gray-700">
                                        Saya menyatakan bahwa tugas yang dikumpulkan adalah hasil karya sendiri dan sesuai dengan ketentuan yang diberikan.
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex items-center justify-end space-x-4">
                            <a href="{{ route('tasks.show', $task) }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Batal
                            </a>
                            <x-primary-button>
                                {{ __('Kumpulkan Tugas') }}
                            </x-primary-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // File size validation
        document.getElementById('file').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const maxSize = 5 * 1024 * 1024; // 5MB in bytes
                if (file.size > maxSize) {
                    alert('File terlalu besar! Maksimal ukuran file adalah 5MB.');
                    e.target.value = '';
                }
            }
        });

        // Form submission confirmation
        document.querySelector('form').addEventListener('submit', function(e) {
            if (!confirm('Apakah Anda yakin ingin mengumpulkan tugas ini? Setelah dikumpulkan, tugas tidak dapat diubah.')) {
                e.preventDefault();
            }
        });
    </script>
</x-app-layout>
