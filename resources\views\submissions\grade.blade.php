<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Berikan Nilai') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Info Submission -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Detail Submission</h3>
                        
                        <dl class="space-y-3 mb-6">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Tugas</dt>
                                <dd class="text-sm text-gray-900">{{ $submission->task->title }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Mahasiswa</dt>
                                <dd class="text-sm text-gray-900">{{ $submission->mahasiswa->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">NIM</dt>
                                <dd class="text-sm text-gray-900">{{ $submission->mahasiswa->nim }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Dikumpulkan</dt>
                                <dd class="text-sm text-gray-900">{{ $submission->created_at->format('d M Y H:i') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Deadline</dt>
                                <dd class="text-sm {{ $submission->created_at > $submission->task->deadline ? 'text-red-600 font-semibold' : 'text-gray-900' }}">
                                    {{ $submission->task->deadline->format('d M Y H:i') }}
                                    @if($submission->created_at > $submission->task->deadline)
                                        <span class="block text-xs text-red-500">
                                            (Terlambat {{ $submission->task->deadline->diffForHumans($submission->created_at) }})
                                        </span>
                                    @endif
                                </dd>
                            </div>
                        </dl>

                        @if($submission->comment)
                            <div class="border-t pt-4">
                                <h4 class="text-md font-semibold text-gray-900 mb-2">Komentar Mahasiswa</h4>
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <p class="text-sm text-gray-700">{{ $submission->comment }}</p>
                                </div>
                            </div>
                        @endif

                        <div class="border-t pt-4 mt-4">
                            <h4 class="text-md font-semibold text-gray-900 mb-2">File Submission</h4>
                            <a href="{{ route('submissions.download', $submission) }}" class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-700 text-white font-bold rounded">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                                Download File
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Form Penilaian -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-6">Form Penilaian</h3>
                        
                        <form action="{{ route('submissions.update', $submission) }}" method="POST" class="space-y-6">
                            @csrf
                            @method('PUT')

                            <!-- Score -->
                            <div>
                                <x-input-label for="score" :value="__('Nilai (0-100)')" />
                                <x-text-input id="score" name="score" type="number" min="0" max="100" class="mt-1 block w-full" :value="old('score', $submission->score)" required autofocus />
                                <x-input-error class="mt-2" :messages="$errors->get('score')" />
                                <p class="mt-1 text-sm text-gray-600">Berikan nilai antara 0-100 untuk submission ini.</p>
                            </div>

                            <!-- Feedback -->
                            <div>
                                <x-input-label for="feedback" :value="__('Feedback untuk Mahasiswa')" />
                                <textarea id="feedback" name="feedback" rows="6" class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm" placeholder="Berikan feedback konstruktif untuk mahasiswa...">{{ old('feedback', $submission->feedback) }}</textarea>
                                <x-input-error class="mt-2" :messages="$errors->get('feedback')" />
                                <p class="mt-1 text-sm text-gray-600">Berikan feedback yang membantu mahasiswa memahami penilaian dan cara meningkatkan kualitas tugas.</p>
                            </div>

                            <!-- Grading Guidelines -->
                            <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-blue-800">Panduan Penilaian</h3>
                                        <div class="mt-2 text-sm text-blue-700">
                                            <ul class="list-disc list-inside space-y-1">
                                                <li><strong>90-100:</strong> Sangat Baik - Memenuhi semua kriteria dengan sempurna</li>
                                                <li><strong>80-89:</strong> Baik - Memenuhi sebagian besar kriteria dengan baik</li>
                                                <li><strong>70-79:</strong> Cukup - Memenuhi kriteria minimum</li>
                                                <li><strong>60-69:</strong> Kurang - Perlu perbaikan signifikan</li>
                                                <li><strong>0-59:</strong> Tidak Memenuhi - Tidak memenuhi kriteria</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Score Buttons -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Nilai Cepat</label>
                                <div class="grid grid-cols-5 gap-2">
                                    <button type="button" onclick="setScore(100)" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-3 rounded text-sm">
                                        A (100)
                                    </button>
                                    <button type="button" onclick="setScore(85)" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-3 rounded text-sm">
                                        B (85)
                                    </button>
                                    <button type="button" onclick="setScore(75)" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-3 rounded text-sm">
                                        C (75)
                                    </button>
                                    <button type="button" onclick="setScore(65)" class="bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-3 rounded text-sm">
                                        D (65)
                                    </button>
                                    <button type="button" onclick="setScore(50)" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-3 rounded text-sm">
                                        E (50)
                                    </button>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex items-center justify-end space-x-4 pt-4 border-t">
                                <a href="{{ route('submissions.show', $submission) }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                    Batal
                                </a>
                                <x-primary-button>
                                    {{ __('Simpan Nilai') }}
                                </x-primary-button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Task Description Reference -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mt-6">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Deskripsi Tugas (Referensi)</h3>
                    <div class="prose max-w-none">
                        <div class="text-gray-700 whitespace-pre-line">{{ $submission->task->description }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function setScore(score) {
            document.getElementById('score').value = score;
        }

        // Form submission confirmation
        document.querySelector('form').addEventListener('submit', function(e) {
            const score = document.getElementById('score').value;
            if (!confirm(`Apakah Anda yakin ingin memberikan nilai ${score} untuk submission ini?`)) {
                e.preventDefault();
            }
        });

        // Score validation
        document.getElementById('score').addEventListener('input', function(e) {
            const value = parseInt(e.target.value);
            if (value < 0) e.target.value = 0;
            if (value > 100) e.target.value = 100;
        });
    </script>
</x-app-layout>
