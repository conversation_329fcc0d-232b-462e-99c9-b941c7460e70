<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            @if(auth()->user()->isDosen())
                {{ __('Daftar Submissions') }}
            @else
                {{ __('Tugas Saya') }}
            @endif
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    @if($submissions->count() > 0)
                        <div class="grid gap-6">
                            @foreach($submissions as $submission)
                                <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-3 mb-2">
                                                <h3 class="text-lg font-semibold text-gray-900">{{ $submission->task->title }}</h3>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                    {{ $submission->isGraded() ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                                    {{ $submission->isGraded() ? 'Sudah Dinilai' : 'Menunggu Penilaian' }}
                                                </span>
                                                @if($submission->created_at > $submission->task->deadline)
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                        Terlambat
                                                    </span>
                                                @endif
                                            </div>
                                            
                                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                                                @if(auth()->user()->isDosen())
                                                    <div>
                                                        <span class="font-medium">Mahasiswa:</span> {{ $submission->mahasiswa->name }}
                                                    </div>
                                                    <div>
                                                        <span class="font-medium">NIM:</span> {{ $submission->mahasiswa->nim }}
                                                    </div>
                                                @else
                                                    <div>
                                                        <span class="font-medium">Dosen:</span> {{ $submission->task->dosen->name }}
                                                    </div>
                                                @endif
                                                <div>
                                                    <span class="font-medium">Dikumpulkan:</span> {{ $submission->created_at->format('d M Y H:i') }}
                                                </div>
                                                <div>
                                                    <span class="font-medium">Deadline:</span> 
                                                    <span class="{{ $submission->created_at > $submission->task->deadline ? 'text-red-600 font-semibold' : '' }}">
                                                        {{ $submission->task->deadline->format('d M Y H:i') }}
                                                    </span>
                                                </div>
                                                @if($submission->isGraded())
                                                    <div>
                                                        <span class="font-medium">Nilai:</span> 
                                                        <span class="font-semibold {{ $submission->score >= 80 ? 'text-green-600' : ($submission->score >= 60 ? 'text-yellow-600' : 'text-red-600') }}">
                                                            {{ $submission->score }}/100
                                                        </span>
                                                    </div>
                                                @endif
                                            </div>

                                            @if($submission->comment)
                                                <div class="mb-3">
                                                    <span class="text-sm font-medium text-gray-700">Komentar:</span>
                                                    <p class="text-sm text-gray-600">{{ Str::limit($submission->comment, 100) }}</p>
                                                </div>
                                            @endif

                                            @if($submission->isGraded() && $submission->feedback)
                                                <div class="mb-3">
                                                    <span class="text-sm font-medium text-gray-700">Feedback Dosen:</span>
                                                    <p class="text-sm text-gray-600">{{ Str::limit($submission->feedback, 100) }}</p>
                                                </div>
                                            @endif

                                            <div class="flex items-center space-x-3">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    File Terlampir
                                                </span>
                                                @if($submission->isGraded())
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                        {{ $submission->score >= 80 ? 'bg-green-100 text-green-800' : ($submission->score >= 60 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                                        @if($submission->score >= 90) A - Sangat Baik
                                                        @elseif($submission->score >= 80) B - Baik
                                                        @elseif($submission->score >= 70) C - Cukup
                                                        @elseif($submission->score >= 60) D - Kurang
                                                        @else E - Tidak Memenuhi
                                                        @endif
                                                    </span>
                                                @endif
                                            </div>
                                        </div>

                                        <div class="flex flex-col space-y-2 ml-4">
                                            <a href="{{ route('submissions.show', $submission) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm text-center">
                                                Lihat Detail
                                            </a>
                                            
                                            <a href="{{ route('submissions.download', $submission) }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm text-center">
                                                Download
                                            </a>

                                            @if(auth()->user()->isDosen() && $submission->task->dosen_id === auth()->id())
                                                @if(!$submission->isGraded())
                                                    <a href="{{ route('submissions.edit', $submission) }}" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded text-sm text-center">
                                                        Nilai
                                                    </a>
                                                @else
                                                    <a href="{{ route('submissions.edit', $submission) }}" class="bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded text-sm text-center">
                                                        Edit Nilai
                                                    </a>
                                                @endif
                                                <form action="{{ route('submissions.destroy', $submission) }}" method="POST" class="inline" onsubmit="return confirm('Apakah Anda yakin ingin menghapus submission ini?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded text-sm">
                                                        Hapus
                                                    </button>
                                                </form>
                                            @endif

                                            @if(auth()->user()->isMahasiswa() && $submission->mahasiswa_id === auth()->id())
                                                <a href="{{ route('tasks.show', $submission->task) }}" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded text-sm text-center">
                                                    Lihat Tugas
                                                </a>
                                                @if(!$submission->isGraded())
                                                    <form action="{{ route('submissions.destroy', $submission) }}" method="POST" class="inline" onsubmit="return confirm('Apakah Anda yakin ingin menghapus submission ini?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded text-sm">
                                                            Hapus
                                                        </button>
                                                    </form>
                                                @endif
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        <div class="mt-6">
                            {{ $submissions->links() }}
                        </div>
                    @else
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">
                                @if(auth()->user()->isDosen())
                                    Tidak ada submission
                                @else
                                    Belum ada tugas yang dikumpulkan
                                @endif
                            </h3>
                            <p class="mt-1 text-sm text-gray-500">
                                @if(auth()->user()->isDosen())
                                    Belum ada mahasiswa yang mengumpulkan tugas untuk mata kuliah Anda.
                                @else
                                    Anda belum mengumpulkan tugas apapun. Lihat daftar tugas yang tersedia.
                                @endif
                            </p>
                            @if(auth()->user()->isMahasiswa())
                                <div class="mt-6">
                                    <a href="{{ route('tasks.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                        <svg class="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                                        </svg>
                                        Lihat Tugas Tersedia
                                    </a>
                                </div>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
