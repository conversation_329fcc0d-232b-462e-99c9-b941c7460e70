<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Detail Submission') }}
            </h2>
            <a href="{{ route('submissions.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Kembali
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Detail Submission -->
                <div class="lg:col-span-2">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center space-x-3 mb-6">
                                <h3 class="text-lg font-semibold text-gray-900">{{ $submission->task->title }}</h3>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium 
                                    {{ $submission->isGraded() ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                    {{ $submission->isGraded() ? 'Sudah Dinilai' : 'Menunggu Penilaian' }}
                                </span>
                                @if($submission->created_at > $submission->task->deadline)
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                        Terlambat
                                    </span>
                                @endif
                            </div>

                            <!-- Task Description -->
                            <div class="mb-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-2">Deskripsi Tugas</h4>
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="text-gray-700 whitespace-pre-line">{{ $submission->task->description }}</div>
                                </div>
                            </div>

                            <!-- Submission Comment -->
                            @if($submission->comment)
                                <div class="mb-6">
                                    <h4 class="text-md font-semibold text-gray-900 mb-2">Komentar Mahasiswa</h4>
                                    <div class="bg-blue-50 rounded-lg p-4">
                                        <p class="text-gray-700">{{ $submission->comment }}</p>
                                    </div>
                                </div>
                            @endif

                            <!-- File Submission -->
                            <div class="mb-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-2">File Submission</h4>
                                <div class="flex items-center space-x-3">
                                    <a href="{{ route('submissions.download', $submission) }}" class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-700 text-white font-bold rounded">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                        Download File
                                    </a>
                                    <span class="text-sm text-gray-600">{{ basename($submission->file_path) }}</span>
                                </div>
                            </div>

                            <!-- Grading Section -->
                            @if($submission->isGraded())
                                <div class="border-t pt-6">
                                    <h4 class="text-md font-semibold text-gray-900 mb-4">Penilaian</h4>
                                    
                                    <div class="bg-green-50 rounded-lg p-4 mb-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h5 class="text-lg font-semibold text-green-800">Nilai: {{ $submission->score }}/100</h5>
                                                <p class="text-sm text-green-600">
                                                    @if($submission->score >= 90) A - Sangat Baik
                                                    @elseif($submission->score >= 80) B - Baik
                                                    @elseif($submission->score >= 70) C - Cukup
                                                    @elseif($submission->score >= 60) D - Kurang
                                                    @else E - Tidak Memenuhi
                                                    @endif
                                                </p>
                                            </div>
                                            <div class="text-right">
                                                <div class="w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold text-white
                                                    {{ $submission->score >= 80 ? 'bg-green-500' : ($submission->score >= 60 ? 'bg-yellow-500' : 'bg-red-500') }}">
                                                    {{ $submission->score }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    @if($submission->feedback)
                                        <div>
                                            <h5 class="text-md font-semibold text-gray-900 mb-2">Feedback dari Dosen</h5>
                                            <div class="bg-gray-50 rounded-lg p-4">
                                                <p class="text-gray-700 whitespace-pre-line">{{ $submission->feedback }}</p>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            @else
                                <div class="border-t pt-6">
                                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <h3 class="text-sm font-medium text-yellow-800">Menunggu Penilaian</h3>
                                                <div class="mt-2 text-sm text-yellow-700">
                                                    <p>Submission Anda sedang dalam proses penilaian oleh dosen. Anda akan mendapat notifikasi setelah tugas dinilai.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Sidebar Info -->
                <div class="space-y-6">
                    <!-- Info Submission -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Informasi Submission</h3>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Mahasiswa</dt>
                                    <dd class="text-sm text-gray-900">{{ $submission->mahasiswa->name }}</dd>
                                </div>
                                @if(auth()->user()->isDosen())
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">NIM</dt>
                                        <dd class="text-sm text-gray-900">{{ $submission->mahasiswa->nim }}</dd>
                                    </div>
                                @endif
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Dosen</dt>
                                    <dd class="text-sm text-gray-900">{{ $submission->task->dosen->name }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Dikumpulkan</dt>
                                    <dd class="text-sm text-gray-900">{{ $submission->created_at->format('d M Y H:i') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Deadline</dt>
                                    <dd class="text-sm {{ $submission->created_at > $submission->task->deadline ? 'text-red-600 font-semibold' : 'text-gray-900' }}">
                                        {{ $submission->task->deadline->format('d M Y H:i') }}
                                        @if($submission->created_at > $submission->task->deadline)
                                            <br><span class="text-xs text-red-500">
                                                (Terlambat {{ $submission->task->deadline->diffForHumans($submission->created_at) }})
                                            </span>
                                        @endif
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd class="text-sm">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                            {{ $submission->isGraded() ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                            {{ $submission->isGraded() ? 'Sudah Dinilai' : 'Menunggu Penilaian' }}
                                        </span>
                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>

                    <!-- Actions -->
                    @if(auth()->user()->isDosen() && $submission->task->dosen_id === auth()->id())
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Aksi Dosen</h3>
                                <div class="space-y-3">
                                    @if(!$submission->isGraded())
                                        <a href="{{ route('submissions.edit', $submission) }}" class="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-center block">
                                            Berikan Nilai
                                        </a>
                                    @else
                                        <a href="{{ route('submissions.edit', $submission) }}" class="w-full bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded text-center block">
                                            Edit Nilai
                                        </a>
                                    @endif
                                    <a href="{{ route('tasks.show', $submission->task) }}" class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center block">
                                        Lihat Tugas
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if(auth()->user()->isMahasiswa() && $submission->mahasiswa_id === auth()->id())
                        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Aksi Mahasiswa</h3>
                                <div class="space-y-3">
                                    <a href="{{ route('tasks.show', $submission->task) }}" class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center block">
                                        Lihat Tugas
                                    </a>
                                    @if(!$submission->isGraded())
                                        <form action="{{ route('submissions.destroy', $submission) }}" method="POST" onsubmit="return confirm('Apakah Anda yakin ingin menghapus submission ini?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                                Hapus Submission
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
